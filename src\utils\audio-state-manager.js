// 音频状态管理器 - 协调音频初始化，避免冲突
class AudioStateManager {
    constructor() {
        this.isInitializing = false;
        this.initPromise = null;
        this.audioElement = null;
        this.audioContext = null;
        this.audioSource = null;
        this.connectedAnalysers = new Set();
        this.initQueue = [];
    }

    // 获取单例实例
    static getInstance() {
        if (!window.audioStateManager) {
            window.audioStateManager = new AudioStateManager();
        }
        return window.audioStateManager;
    }

    // 安全地初始化音频上下文和源
    async initializeAudio(audioElement) {
        // 如果正在初始化，等待完成
        if (this.isInitializing && this.initPromise) {
            await this.initPromise;
            return {
                audioContext: this.audioContext,
                audioSource: this.audioSource,
                audioElement: this.audioElement
            };
        }

        // 如果已经初始化完成，直接返回
        if (this.audioContext && this.audioSource && this.audioElement === audioElement) {
            return {
                audioContext: this.audioContext,
                audioSource: this.audioSource,
                audioElement: this.audioElement
            };
        }

        // 开始初始化
        this.isInitializing = true;
        this.initPromise = this._doInitialize(audioElement);
        
        try {
            const result = await this.initPromise;
            return result;
        } finally {
            this.isInitializing = false;
            this.initPromise = null;
        }
    }

    async _doInitialize(audioElement) {
        console.log('AudioStateManager: 开始初始化音频');
        
        // 保存播放状态
        const wasPlaying = audioElement && !audioElement.paused;
        const currentTime = audioElement ? audioElement.currentTime : 0;

        try {
            // 暂停音频以避免初始化时的中断
            if (wasPlaying && audioElement) {
                audioElement.pause();
            }

            // 创建或获取全局音频上下文
            if (!window.globalAudioContext) {
                window.globalAudioContext = new (window.AudioContext || window.webkitAudioContext)();
            }

            // 确保音频上下文处于运行状态
            if (window.globalAudioContext.state !== 'running') {
                await window.globalAudioContext.resume();
            }

            this.audioContext = window.globalAudioContext;
            this.audioElement = audioElement;

            // 创建音频源（如果尚未创建）
            if (!audioElement.audioSource) {
                audioElement.audioSource = this.audioContext.createMediaElementSource(audioElement);
                
                // 连接到音频输出
                audioElement.audioSource.connect(this.audioContext.destination);
                audioElement.isConnectedToDestination = true;
            }

            this.audioSource = audioElement.audioSource;

            console.log('AudioStateManager: 音频初始化完成');

            // 恢复播放状态
            if (wasPlaying && audioElement) {
                setTimeout(() => {
                    audioElement.currentTime = currentTime;
                    audioElement.play().catch(e => console.warn('恢复播放失败:', e));
                }, 100);
            }

            return {
                audioContext: this.audioContext,
                audioSource: this.audioSource,
                audioElement: this.audioElement
            };

        } catch (error) {
            console.error('AudioStateManager: 初始化失败', error);
            throw error;
        }
    }

    // 为分析器创建连接
    async connectAnalyser(analyser, analyserId) {
        if (!this.audioSource) {
            throw new Error('音频源尚未初始化');
        }

        if (!this.connectedAnalysers.has(analyserId)) {
            this.audioSource.connect(analyser);
            this.connectedAnalysers.add(analyserId);
            console.log(`AudioStateManager: 连接分析器 ${analyserId}`);
        }
    }

    // 断开分析器连接
    disconnectAnalyser(analyser, analyserId) {
        if (this.audioSource && this.connectedAnalysers.has(analyserId)) {
            try {
                this.audioSource.disconnect(analyser);
                this.connectedAnalysers.delete(analyserId);
                console.log(`AudioStateManager: 断开分析器 ${analyserId}`);
            } catch (e) {
                console.warn(`断开分析器 ${analyserId} 失败:`, e);
            }
        }
    }

    // 清理资源
    cleanup() {
        console.log('AudioStateManager: 清理资源');
        
        this.connectedAnalysers.clear();
        this.audioElement = null;
        this.audioSource = null;
        this.audioContext = null;
        this.isInitializing = false;
        this.initPromise = null;
    }

    // 获取当前状态
    getState() {
        return {
            isInitializing: this.isInitializing,
            hasAudioContext: !!this.audioContext,
            hasAudioSource: !!this.audioSource,
            hasAudioElement: !!this.audioElement,
            connectedAnalysers: Array.from(this.connectedAnalysers)
        };
    }
}

// 导出单例实例
export default AudioStateManager.getInstance();
