<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重庆地图定位</title>
    <link rel="stylesheet" href="src/styles/style.css">
    <!-- 依赖引入顺序很重要 -->
    <script src="rule/jquery.js"></script>
    <script src="rule/echarts.min.js"></script>
    <script src="rule/bmap.min.js"></script>
    <script src="https://api.map.baidu.com/api?v=3.0&ak=lgj1icmXqYZaTf7V9WOs7Hj1FspANFSE"></script>
</head>
<body>
<section class="section-card glass" style="margin-bottom:18px;">
        <a href="#home" class="back-btn" style="margin-top: 35px;">← 返回首页</a>
        <h2 style="text-align: center;">重庆地图定位</h2>
        <div style="color:#eaf6ff;opacity:0.8;font-size:1rem;margin-bottom:10px;">重庆理工大学主要校区定位与行政区遮罩</div>
    </section>
    <div id="Map" style="width:100%;height:800px;margin-bottom:32px;"></div>
    <script type="module">
        import { initMap } from 'src/visualizations/components/map.js';
        window.addEventListener('DOMContentLoaded', () => {
            try {
                initMap();
            } catch (error) {
                console.error('Error initializing map:', error);
            }
        });
    </script>
</body>
</html>
