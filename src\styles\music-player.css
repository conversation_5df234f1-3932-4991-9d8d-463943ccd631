/* 重置所有元素 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  border: none;
  outline: none;
}

html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  background: transparent;
}

#music-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 10px;
  overflow: hidden;
  border: 1.5px solid rgba(126, 203, 255, 0.6);
  background: rgba(41, 98, 255, 0.4);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 自定义音频播放器 */
.audio-wrapper {
  width: 100%;
  flex-grow: 1;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 隐藏原生播放器 */
audio {
  position: absolute;
  visibility: hidden;
  width: 1px;
  height: 1px;
}

/* 进度条 */
.progress-container {
  width: 100%;
  height: 6px;
  background: rgba(30, 60, 120, 0.4);
  cursor: pointer;
  position: relative;
}

.progress-bar {
  height: 100%;
  width: 0;
  background: rgba(126, 203, 255, 0.8);
  border-radius: 0 3px 3px 0;
}

/* 控制条 */
.player-controls {
  width: 100%;
  padding: 5px 8px;
  display: flex;
  align-items: center;
  background: rgba(41, 98, 255, 0.4);
}

.play-pause, .volume-btn {
  color: #eaf6ff;
  background: transparent;
  border: none;
  font-size: 16px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-pause:hover, .volume-btn:hover {
  opacity: 0.8;
}

.time {
  color: #eaf6ff;
  font-size: 12px;
  margin: 0 8px;
  min-width: 60px;
  text-align: center;
}

.volume-container {
  position: relative;
  display: flex;
  align-items: center;
}

.volume-slider {
  width: 60px;
  height: 4px;
  background: rgba(30, 60, 120, 0.4);
  margin-left: 5px;
  cursor: pointer;
  border-radius: 2px;
}

.volume-percentage {
  height: 100%;
  width: 100%;
  background: rgba(126, 203, 255, 0.8);
  border-radius: 2px;
}

.song-controls {
  display: flex;
  justify-content: space-between;
  background: rgba(41, 98, 255, 0.5);
  border-bottom: 1px solid rgba(126, 203, 255, 0.4);
  padding: 6px;
}

.song-controls button {
  background: rgba(41, 98, 255, 0.4);
  border: 1px solid rgba(126, 203, 255, 0.6);
  color: #aee6ff;
  font-size: 14px;
  cursor: pointer;
  padding: 2px 10px;
  border-radius: 5px;
  transition: all 0.2s ease;
}

.song-controls button:hover {
  background: rgba(41, 98, 255, 0.6);
  transform: scale(1.05);
}

.current-song {
  font-size: 15px;
  color: #eaf6ff;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
  margin: 0 8px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
} 