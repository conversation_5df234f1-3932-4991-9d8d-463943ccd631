<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课程得分饼图</title>
    <link rel="stylesheet" href="src/styles/style.css">
    <!-- 依赖引入顺序很重要 -->
    <script src="rule/jquery.js"></script>
    <script src="D3/d3.v7.min.js"></script>
</head>
<body>
    <div class="container" style="max-width: 1200px;">
        <section class="section-card glass" style="margin-bottom:18px;">
            <a href="#home" class="back-btn">← 返回首页</a>
            <h2 style="text-align: center; margin-top: -20px;">课程得分饼图</h2>
            <div style="color:#eaf6ff;opacity:0.8;font-size:1rem;margin-bottom:10px; text-align: center;">
                可视化展示主要课程成绩分布
            </div>
        </section>
    </div>
    <div id="charts-area" style="max-width:100vw;overflow:visible;display:flex;flex-direction:row;justify-content:center;gap:30px;">
        <div id="pie-chart" style="height:550px;width:600px;margin-top: 0px;"></div>
        <div id="bar-chart" style="height:550px;width:600px;margin-top: 0px;"></div>
    </div>
    <style>
        #pie-chart path {
            cursor: pointer;
        }
        .tooltip {
            position: absolute;
            padding: 8px;
            font-size: 14px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 8px;
            pointer-events: none;
            color: #fff;
            z-index: 1000;
        }
        
        /* 添加媒体查询以适应窄屏 */
        @media (max-width: 1200px) {
            #charts-area {
                flex-direction: column;
                align-items: center;
            }
            #pie-chart, #bar-chart {
                margin-bottom: 20px;
            }
        }
    </style>
    <script type="module">
        import { initCharts } from 'src/visualizations/components/pie.js';
        window.addEventListener('DOMContentLoaded', () => {
            try {
                initCharts();
            } catch (error) {
                console.error('Error initializing charts:', error);
            }
        });
    </script>
</body>
</html>